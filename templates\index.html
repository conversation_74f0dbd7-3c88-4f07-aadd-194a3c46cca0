<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI YouTube Shorts Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen">
    <div class="container mx-auto px-4 py-8" x-data="videoProcessor()">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold text-white mb-4">
                <i class="fas fa-video mr-3 text-blue-400"></i>
                AI Shorts Generator
            </h1>
            <p class="text-xl text-gray-300">Transform your videos into engaging vertical content with AI</p>
        </div>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto">
            <!-- Input Section -->
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20">
                <h2 class="text-2xl font-semibold text-white mb-6 flex items-center">
                    <i class="fas fa-upload mr-3 text-green-400"></i>
                    Upload Your Video
                </h2>
                
                <!-- Tab Navigation -->
                <div class="flex space-x-4 mb-6">
                    <button @click="activeTab = 'youtube'" 
                            :class="activeTab === 'youtube' ? 'bg-red-500 text-white' : 'bg-gray-600 text-gray-300'"
                            class="px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center">
                        <i class="fab fa-youtube mr-2"></i>
                        YouTube URL
                    </button>
                    <button @click="activeTab = 'upload'" 
                            :class="activeTab === 'upload' ? 'bg-blue-500 text-white' : 'bg-gray-600 text-gray-300'"
                            class="px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center">
                        <i class="fas fa-file-upload mr-2"></i>
                        File Upload
                    </button>
                    <button @click="activeTab = 'gdrive'" 
                            :class="activeTab === 'gdrive' ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-300'"
                            class="px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center">
                        <i class="fab fa-google-drive mr-2"></i>
                        Google Drive
                    </button>
                </div>

                <!-- YouTube URL Tab -->
                <div x-show="activeTab === 'youtube'" x-transition class="space-y-4">
                    <div>
                        <label class="block text-white font-medium mb-2">YouTube Video URL</label>
                        <input type="url" x-model="youtubeUrl" 
                               placeholder="https://www.youtube.com/watch?v=..."
                               class="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-blue-400 focus:outline-none transition-colors">
                    </div>
                </div>

                <!-- File Upload Tab -->
                <div x-show="activeTab === 'upload'" x-transition class="space-y-4">
                    <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
                        <input type="file" id="videoFile" accept="video/*" @change="handleFileUpload" class="hidden">
                        <label for="videoFile" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                            <p class="text-white font-medium mb-2">Click to upload video file</p>
                            <p class="text-gray-400">Supports MP4, AVI, MOV, WEBM</p>
                        </label>
                        <div x-show="uploadedFile" class="mt-4 p-3 bg-green-100 rounded-lg">
                            <p class="text-green-800" x-text="uploadedFile?.name"></p>
                        </div>
                    </div>
                </div>

                <!-- Google Drive Tab -->
                <div x-show="activeTab === 'gdrive'" x-transition class="space-y-4">
                    <div>
                        <label class="block text-white font-medium mb-2">Google Drive Share Link</label>
                        <input type="url" x-model="gdriveUrl" 
                               placeholder="https://drive.google.com/file/d/..."
                               class="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-green-400 focus:outline-none transition-colors">
                    </div>
                    <p class="text-gray-400 text-sm">
                        <i class="fas fa-info-circle mr-1"></i>
                        Make sure the Google Drive file is set to "Anyone with the link can view"
                    </p>
                </div>

                <!-- Subtitle Style Selection -->
                <div class="mt-6">
                    <label class="block text-white font-medium mb-3">Subtitle Style</label>
                    <div class="grid grid-cols-3 gap-4">
                        <div @click="subtitleStyle = 'modern'" 
                             :class="subtitleStyle === 'modern' ? 'ring-2 ring-blue-400' : ''"
                             class="bg-gray-800 p-4 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors">
                            <h3 class="text-white font-medium mb-2">Modern</h3>
                            <p class="text-gray-400 text-sm">Clean white text with black outline</p>
                        </div>
                        <div @click="subtitleStyle = 'tiktok'" 
                             :class="subtitleStyle === 'tiktok' ? 'ring-2 ring-blue-400' : ''"
                             class="bg-gray-800 p-4 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors">
                            <h3 class="text-white font-medium mb-2">TikTok</h3>
                            <p class="text-gray-400 text-sm">Bold cyan text, uppercase</p>
                        </div>
                        <div @click="subtitleStyle = 'instagram'" 
                             :class="subtitleStyle === 'instagram' ? 'ring-2 ring-blue-400' : ''"
                             class="bg-gray-800 p-4 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors">
                            <h3 class="text-white font-medium mb-2">Instagram</h3>
                            <p class="text-gray-400 text-sm">Minimal with subtle background</p>
                        </div>
                    </div>
                </div>

                <!-- Process Button -->
                <div class="mt-8">
                    <button @click="processVideo()" 
                            :disabled="processing || !canProcess()"
                            :class="processing ? 'bg-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700'"
                            class="w-full py-4 rounded-lg text-white font-semibold text-lg transition-all duration-200 flex items-center justify-center">
                        <template x-if="!processing">
                            <span class="flex items-center">
                                <i class="fas fa-magic mr-2"></i>
                                Generate AI Short
                            </span>
                        </template>
                        <template x-if="processing">
                            <span class="flex items-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Processing...
                            </span>
                        </template>
                    </button>
                </div>
            </div>

            <!-- Progress Section -->
            <div x-show="processing" x-transition class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-cogs mr-3 text-yellow-400"></i>
                    Processing Status
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-300">Downloading video...</span>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                            <span class="text-blue-400" x-text="progress.download + '%'"></span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300" 
                             :style="`width: ${progress.overall}%`"></div>
                    </div>
                    <p class="text-gray-400 text-sm" x-text="statusMessage"></p>
                </div>
            </div>

            <!-- Results Section -->
            <div x-show="result" x-transition class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-check-circle mr-3 text-green-400"></i>
                    Processing Complete!
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-white font-medium mb-2">Generated Video</h4>
                        <video x-show="result?.video_url" controls class="w-full rounded-lg">
                            <source :src="result?.video_url" type="video/mp4">
                        </video>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-white font-medium mb-2">Download Options</h4>
                            <div class="space-y-2">
                                <a x-show="result?.video_url" :href="result?.video_url" download
                                   class="block w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-center transition-colors">
                                    <i class="fas fa-download mr-2"></i>
                                    Download Video
                                </a>
                                <a x-show="result?.subtitle_file" :href="result?.subtitle_file" download
                                   class="block w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-center transition-colors">
                                    <i class="fas fa-file-alt mr-2"></i>
                                    Download Subtitles
                                </a>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-white font-medium mb-2">Video Info</h4>
                            <div class="bg-gray-800 p-3 rounded-lg text-sm">
                                <p class="text-gray-300">Duration: <span class="text-white" x-text="result?.duration"></span></p>
                                <p class="text-gray-300">Resolution: <span class="text-white" x-text="result?.resolution"></span></p>
                                <p class="text-gray-300">Style: <span class="text-white" x-text="result?.style"></span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Section -->
            <div x-show="error" x-transition class="bg-red-500/20 backdrop-blur-lg rounded-2xl p-8 border border-red-500/50">
                <h3 class="text-xl font-semibold text-red-300 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-3"></i>
                    Error
                </h3>
                <p class="text-red-200" x-text="error"></p>
                <button @click="clearError()" class="mt-4 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                    Dismiss
                </button>
            </div>
        </div>
    </div>

    <script>
        function videoProcessor() {
            return {
                activeTab: 'youtube',
                youtubeUrl: '',
                gdriveUrl: '',
                uploadedFile: null,
                subtitleStyle: 'modern',
                processing: false,
                progress: {
                    download: 0,
                    overall: 0
                },
                statusMessage: '',
                result: null,
                error: null,

                handleFileUpload(event) {
                    this.uploadedFile = event.target.files[0];
                },

                canProcess() {
                    if (this.activeTab === 'youtube') return this.youtubeUrl.trim() !== '';
                    if (this.activeTab === 'upload') return this.uploadedFile !== null;
                    if (this.activeTab === 'gdrive') return this.gdriveUrl.trim() !== '';
                    return false;
                },

                async processVideo() {
                    this.processing = true;
                    this.result = null;
                    this.error = null;
                    this.progress = { download: 0, overall: 0 };
                    this.statusMessage = 'Initializing...';

                    try {
                        const formData = new FormData();
                        formData.append('subtitle_style', this.subtitleStyle);

                        if (this.activeTab === 'youtube') {
                            formData.append('input_type', 'youtube');
                            formData.append('url', this.youtubeUrl);
                        } else if (this.activeTab === 'upload') {
                            formData.append('input_type', 'upload');
                            formData.append('video_file', this.uploadedFile);
                        } else if (this.activeTab === 'gdrive') {
                            formData.append('input_type', 'gdrive');
                            formData.append('url', this.gdriveUrl);
                        }

                        // Start processing
                        const response = await fetch('/api/process', {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        // Check if response is JSON or streaming
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const result = await response.json();
                            if (result.error) {
                                throw new Error(result.error);
                            }
                            this.result = result;
                        } else {
                            // Handle streaming response for progress updates
                            const reader = response.body.getReader();
                            const decoder = new TextDecoder();

                            while (true) {
                                const { done, value } = await reader.read();
                                if (done) break;

                                const chunk = decoder.decode(value);
                                const lines = chunk.split('\n');

                                for (const line of lines) {
                                    if (line.startsWith('data: ')) {
                                        try {
                                            const data = JSON.parse(line.slice(6));
                                            if (data.progress !== undefined) {
                                                this.progress.overall = data.progress;
                                            }
                                            if (data.message) {
                                                this.statusMessage = data.message;
                                            }
                                            if (data.result) {
                                                this.result = data.result;
                                            }
                                            if (data.error) {
                                                throw new Error(data.error);
                                            }
                                        } catch (e) {
                                            // Ignore JSON parse errors for partial chunks
                                        }
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        this.error = error.message;
                    } finally {
                        this.processing = false;
                    }
                },

                clearError() {
                    this.error = null;
                }
            }
        }
    </script>
</body>
</html>