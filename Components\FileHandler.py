import os
import re
import requests
from urllib.parse import urlparse, parse_qs
import tempfile
import shutil

def extract_gdrive_id(url):
    """Extract Google Drive file ID from various URL formats"""
    patterns = [
        r'/file/d/([a-zA-Z0-9-_]+)',
        r'id=([a-zA-Z0-9-_]+)',
        r'/d/([a-zA-Z0-9-_]+)',
        r'drive\.google\.com/.*[?&]id=([a-zA-Z0-9-_]+)',
        r'drive\.google\.com/file/d/([a-zA-Z0-9-_]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def download_from_gdrive(file_id, output_path, progress_callback=None):
    """
    Download file from Google Drive with progress tracking
    
    Args:
        file_id: Google Drive file ID
        output_path: Local path to save the file
        progress_callback: Function to call with progress updates
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Google Drive direct download URL
        download_url = f"https://drive.google.com/uc?id={file_id}&export=download"
        
        session = requests.Session()
        
        if progress_callback:
            progress_callback(5, "Initiating Google Drive download...")
        
        response = session.get(download_url, stream=True)
        
        # Handle the confirmation page for large files
        if 'confirm=' in response.text or response.status_code != 200:
            # Try to find confirmation token
            token = None
            for line in response.text.split('\n'):
                if 'confirm=' in line and 'id=' in line:
                    try:
                        token_match = re.search(r'confirm=([^&]+)', line)
                        if token_match:
                            token = token_match.group(1)
                            break
                    except:
                        pass
            
            if token:
                download_url = f"https://drive.google.com/uc?confirm={token}&id={file_id}&export=download"
                response = session.get(download_url, stream=True)
            else:
                # Try alternative download method
                download_url = f"https://drive.google.com/uc?export=download&id={file_id}"
                response = session.get(download_url, stream=True)
        
        if response.status_code != 200:
            print(f"Failed to download: HTTP {response.status_code}")
            return False
        
        # Get file size if available
        total_size = None
        if 'content-length' in response.headers:
            total_size = int(response.headers['content-length'])
        
        downloaded = 0
        chunk_size = 8192
        
        if progress_callback:
            progress_callback(10, "Downloading file from Google Drive...")
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # Update progress
                    if progress_callback and total_size:
                        progress = 10 + (downloaded / total_size) * 40  # 10% to 50%
                        progress_callback(int(progress), f"Downloaded {downloaded // 1024 // 1024}MB...")
        
        # Verify file was downloaded
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            if progress_callback:
                progress_callback(50, "Google Drive download complete!")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error downloading from Google Drive: {e}")
        return False

def validate_video_file(file_path):
    """
    Validate if the file is a valid video file
    
    Args:
        file_path: Path to the video file
        
    Returns:
        dict: Validation result with status and info
    """
    try:
        import cv2
        
        if not os.path.exists(file_path):
            return {'valid': False, 'error': 'File does not exist'}
        
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return {'valid': False, 'error': 'File is empty'}
        
        # Try to open with OpenCV
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            return {'valid': False, 'error': 'Cannot open video file'}
        
        # Get basic video info
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        # Basic validation
        if frame_count == 0 or fps == 0 or width == 0 or height == 0:
            return {'valid': False, 'error': 'Invalid video properties'}
        
        if duration < 1:
            return {'valid': False, 'error': 'Video too short (minimum 1 second)'}
        
        if duration > 3600:  # 1 hour
            return {'valid': False, 'error': 'Video too long (maximum 1 hour)'}
        
        return {
            'valid': True,
            'duration': duration,
            'fps': fps,
            'resolution': f"{width}x{height}",
            'file_size': file_size,
            'frame_count': frame_count
        }
        
    except Exception as e:
        return {'valid': False, 'error': f'Validation error: {str(e)}'}

def sanitize_filename(filename):
    """
    Sanitize filename for safe storage
    
    Args:
        filename: Original filename
        
    Returns:
        str: Sanitized filename
    """
    # Remove path components
    filename = os.path.basename(filename)
    
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    
    return name + ext

def ensure_video_extension(filename):
    """
    Ensure filename has a video extension
    
    Args:
        filename: Original filename
        
    Returns:
        str: Filename with video extension
    """
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']
    
    name, ext = os.path.splitext(filename.lower())
    
    if ext not in video_extensions:
        return filename + '.mp4'
    
    return filename

def get_file_info(file_path):
    """
    Get comprehensive file information
    
    Args:
        file_path: Path to the file
        
    Returns:
        dict: File information
    """
    try:
        stat = os.stat(file_path)
        
        return {
            'size': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'created': stat.st_ctime,
            'modified': stat.st_mtime,
            'extension': os.path.splitext(file_path)[1].lower(),
            'basename': os.path.basename(file_path)
        }
    except Exception as e:
        return {'error': str(e)}

def cleanup_temp_files(temp_dir=None):
    """
    Clean up temporary files
    
    Args:
        temp_dir: Specific temporary directory to clean
    """
    try:
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"Cleaned up temporary directory: {temp_dir}")
        
        # Also clean up any orphaned temp files in the current directory
        temp_patterns = [
            'temp_*',
            'tmp_*',
            '*.tmp',
            'gdrive_*.mp4',
            'upload_*.mp4'
        ]
        
        import glob
        for pattern in temp_patterns:
            for file in glob.glob(pattern):
                try:
                    os.remove(file)
                    print(f"Cleaned up temp file: {file}")
                except:
                    pass
                    
    except Exception as e:
        print(f"Error during cleanup: {e}")

if __name__ == "__main__":
    # Test functions
    test_url = "https://drive.google.com/file/d/1ABC123/view"
    file_id = extract_gdrive_id(test_url)
    print(f"Extracted file ID: {file_id}")
    
    test_filename = "My Video File (2024).mp4"
    sanitized = sanitize_filename(test_filename)
    print(f"Sanitized filename: {sanitized}")