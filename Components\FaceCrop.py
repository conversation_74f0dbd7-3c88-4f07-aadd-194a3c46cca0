import cv2
import numpy as np
import os
from moviepy.editor import *
from Components.Speaker import detect_faces_and_speakers, Frames
from Components.SubtitleGenerator import overlay_subtitles_on_video, create_output_folder, generate_output_filename
global Fps

# ===== FACE TRACKING CONFIGURATION =====
# Adjust these values to fine-tune face tracking and reduce flickering
# Lower smoothing_factor = smoother movement but slower response
# Higher max_movement_per_frame = more responsive but potentially more jittery
FACE_TRACKING_CONFIG = {
    'default_smoothing_factor': 0.08,      # Reduced for smoother movement
    'default_max_movement_per_frame': 4,   # Reduced for less jitter
    'confidence_threshold': 0.7,           # Face detection confidence (0.5-0.9)
    'max_no_face_frames': 30,              # Frames before fallback to center (15-60)
    'face_area_min_ratio': 0.01,           # Min face area ratio (0.005-0.02)
    'face_area_max_ratio': 0.5,            # Max face area ratio (0.3-0.7)
    'edge_margin': 10,                     # Margin from edges (5-20)
    'position_change_threshold': 15        # Minimum pixel change to trigger movement
}

# Add FFmpeg to PATH
ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                          "ffmpeg-master-latest-win64-gpl", "bin")
if os.path.exists(ffmpeg_path):
    os.environ["PATH"] += os.pathsep + ffmpeg_path
    print(f"Added FFmpeg to PATH in FaceCrop.py: {ffmpeg_path}")

def set_face_tracking_smoothness(smoothness_level='medium'):
    """
    Adjust face tracking smoothness to reduce flickering.

    Args:
        smoothness_level (str): 'low', 'medium', 'high', or 'ultra'
            - 'low': More responsive, may have slight flickering
            - 'medium': Balanced (default)
            - 'high': Smoother, slower response
            - 'ultra': Very smooth, slowest response

    Returns:
        tuple: (smoothing_factor, max_movement_per_frame)
    """
    settings = {
        'low': (0.25, 12),
        'medium': (0.12, 6),
        'high': (0.08, 4),
        'ultra': (0.05, 2)
    }

    if smoothness_level not in settings:
        print(f"Warning: Unknown smoothness level '{smoothness_level}', using 'medium'")
        smoothness_level = 'medium'

    smoothing_factor, max_movement = settings[smoothness_level]
    print(f"Face tracking set to '{smoothness_level}': smoothing={smoothing_factor}, max_movement={max_movement}")
    return smoothing_factor, max_movement

def crop_to_vertical(input_video_path, output_video_path,
                    smoothing_factor=None, max_movement_per_frame=None):
    detect_faces_and_speakers(input_video_path, "DecOut.mp4")

    # Also save intermediate files to output folder
    output_folder = create_output_folder()
    base_name = os.path.splitext(os.path.basename(output_video_path))[0]
    organized_output = os.path.join(output_folder, generate_output_filename(f"{base_name}_raw_cropped.mp4", ""))
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

    cap = cv2.VideoCapture(input_video_path, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        print("Error: Could not open video.")
        return

    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    vertical_height = int(original_height)
    vertical_width = int(vertical_height * 9 / 16)
    print(vertical_height, vertical_width)


    if original_width < vertical_width:
        print("Error: Original video width is less than the desired vertical width.")
        return

    x_start = (original_width - vertical_width) // 2
    x_end = x_start + vertical_width
    print(f"start and end - {x_start} , {x_end}")
    print(x_end-x_start)
    half_width = vertical_width // 2

    fourcc = cv2.VideoWriter_fourcc(*'H264')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (vertical_width, vertical_height))
    global Fps
    Fps = fps
    print(fps)
    count = 0

    # Use configuration values or defaults
    if smoothing_factor is None:
        smoothing_factor = FACE_TRACKING_CONFIG['default_smoothing_factor']
    if max_movement_per_frame is None:
        max_movement_per_frame = FACE_TRACKING_CONFIG['default_max_movement_per_frame']

    confidence_threshold = FACE_TRACKING_CONFIG['confidence_threshold']
    max_no_face_frames = FACE_TRACKING_CONFIG['max_no_face_frames']
    face_area_min_ratio = FACE_TRACKING_CONFIG['face_area_min_ratio']
    face_area_max_ratio = FACE_TRACKING_CONFIG['face_area_max_ratio']
    edge_margin = FACE_TRACKING_CONFIG['edge_margin']
    position_threshold = FACE_TRACKING_CONFIG['position_change_threshold']

    print(f"Face tracking settings: smoothing={smoothing_factor}, max_movement={max_movement_per_frame}")
    print(f"Additional settings: confidence={confidence_threshold}, max_no_face={max_no_face_frames}")

    # Position tracking
    smooth_x_start = float(x_start)
    smooth_x_end = float(x_end)
    position_history = []
    no_face_counter = 0
    max_no_face_frames = 30  # Frames to wait before falling back to center

    def smooth_position(current_pos, target_pos, smoothing_factor, max_movement, threshold=15):
        """Apply exponential smoothing with movement limits and threshold"""
        diff = target_pos - current_pos
        
        # Only move if change is significant enough
        if abs(diff) < threshold:
            return current_pos
            
        # Limit maximum movement per frame
        if abs(diff) > max_movement:
            diff = max_movement if diff > 0 else -max_movement
        return current_pos + (diff * smoothing_factor)

    def validate_face_detection(face, frame_width):
        """Validate face detection based on size and position consistency"""
        x, _, w, h = face

        # Check if face is reasonable size (not too small or too large)
        face_area = w * h
        frame_area = frame_width * frame_width  # Approximate
        area_ratio = face_area / frame_area

        if area_ratio < face_area_min_ratio or area_ratio > face_area_max_ratio:
            return False

        # Check if face position is reasonable (not at extreme edges)
        if x < edge_margin or x + w > frame_width - edge_margin:
            return False

        return True

    for _ in range(total_frames):
        ret, frame = cap.read()
        if not ret:
            print("Error: Could not read frame.")
            break
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

        # Filter faces by validation
        valid_faces = [face for face in faces if validate_face_detection(face, original_width)]

        # Initialize target position
        target_center_x = original_width // 2  # Default to center
        face_detected = False

        # Try to get the active speaker location from Frames
        if len(Frames) > count:
            try:
                (speaker_x, speaker_y, speaker_w, speaker_h) = Frames[count]

                # If we have valid detected faces, find the one closest to the active speaker
                if len(valid_faces) > 0:
                    best_face = None
                    min_distance = float('inf')

                    for face in valid_faces:
                        face_x, face_y, face_w, face_h = face
                        face_center_x = face_x + face_w // 2
                        speaker_center_x = speaker_x + speaker_w // 2

                        # Calculate distance between face center and speaker center
                        distance = abs(face_center_x - speaker_center_x)

                        if distance < min_distance:
                            min_distance = distance
                            best_face = face

                    # Use the best matching face if found and close enough
                    if best_face is not None and min_distance < original_width // 4:
                        x, y, w, h = best_face
                        target_center_x = x + (w // 2)
                        face_detected = True
                        print(f"Using detected face: x={x}, w={w}, center={target_center_x}")

                # If no good face match, use speaker data directly
                if not face_detected:
                    target_center_x = speaker_x + (speaker_w // 2)
                    face_detected = True
                    print(f"Using speaker data: center={target_center_x}")

            except Exception as e:
                print(f"Error accessing Frames[{count}]: {e}")

        # Fallback: use first valid detected face or keep current position
        if not face_detected:
            if len(valid_faces) > 0:
                x, y, w, h = valid_faces[0]
                target_center_x = x + (w // 2)
                face_detected = True
                print(f"Using first valid face: center={target_center_x}")
                no_face_counter = 0
            else:
                # No face detected - increment counter
                no_face_counter += 1
                if no_face_counter > max_no_face_frames:
                    # Fall back to center after too many frames without face
                    target_center_x = original_width // 2
                    print(f"No face for {no_face_counter} frames, falling back to center")
                else:
                    # Keep current position
                    target_center_x = smooth_x_start + half_width
                    print(f"No face detected, keeping current position: {target_center_x}")
        else:
            no_face_counter = 0

        # Calculate target crop window
        target_x_start = target_center_x - half_width
        target_x_end = target_center_x + half_width

        # Apply boundary constraints
        if target_x_start < 0:
            target_x_start = 0
            target_x_end = vertical_width
        elif target_x_end > original_width:
            target_x_end = original_width
            target_x_start = original_width - vertical_width

        # Apply smooth movement
        if count == 0:
            # First frame - use target position directly
            smooth_x_start = float(target_x_start)
            smooth_x_end = float(target_x_end)
        else:
            # Apply exponential smoothing with threshold
            smooth_x_start = smooth_position(smooth_x_start, target_x_start, smoothing_factor, max_movement_per_frame, position_threshold)
            smooth_x_end = smooth_position(smooth_x_end, target_x_end, smoothing_factor, max_movement_per_frame, position_threshold)

        # Convert to integers and ensure bounds
        x_start = int(max(0, min(smooth_x_start, original_width - vertical_width)))
        x_end = x_start + vertical_width

        # Store position in history
        position_history.append((x_start, target_center_x, face_detected))
        if len(position_history) > 10:  # Keep only last 10 positions
            position_history.pop(0)

        # Debug output (reduced frequency)
        if count % 30 == 0:  # Print every 30 frames instead of every frame
            print(f"Frame {count}: target_center={target_center_x}, smooth_start={smooth_x_start:.1f}, final_start={x_start}")

        count += 1

        # Crop the frame
        cropped_frame = frame[:, x_start:x_end]

        # Final safety check
        if cropped_frame.shape[1] != vertical_width or cropped_frame.shape[1] == 0:
            print(f"Warning: Invalid crop size {cropped_frame.shape}, using center crop")
            x_start = (original_width - vertical_width) // 2
            x_end = x_start + vertical_width
            cropped_frame = frame[:, x_start:x_end]

        out.write(cropped_frame)

    cap.release()
    out.release()
    print("Cropping complete. The video has been saved to", output_video_path, count)

    # Also save a copy to the organized output folder
    try:
        import shutil
        shutil.copy2(output_video_path, organized_output)
        print(f"✅ Raw cropped video also saved to: {organized_output}")
    except Exception as e:
        print(f"Warning: Could not save organized copy: {e}")



def combine_videos(video_with_audio, video_without_audio, output_filename, transcriptions=None, subtitle_style='modern'):
    try:
        # Create output folder
        output_folder = create_output_folder()

        # Generate organized filenames
        base_name = os.path.splitext(os.path.basename(output_filename))[0]

        # Create different output files in the output folder
        cropped_output = os.path.join(output_folder, generate_output_filename(f"{base_name}_cropped.mp4", ""))
        final_output_path = os.path.join(output_folder, generate_output_filename(f"{base_name}_final.mp4", ""))

        # Load video clips
        clip_with_audio = VideoFileClip(video_with_audio)
        clip_without_audio = VideoFileClip(video_without_audio)

        audio = clip_with_audio.audio
        combined_clip = clip_without_audio.set_audio(audio)

        global Fps

        # Save cropped video without subtitles first (optimized settings)
        print(f"Saving cropped video to: {cropped_output}")
        combined_clip.write_videofile(
            cropped_output, 
            codec='libx264', 
            audio_codec='aac', 
            fps=Fps, 
            preset='faster',   # Faster encoding
            bitrate='2500k',   # Optimized bitrate
            threads=4,         # Multi-threading
            verbose=False,     # Reduce output
            logger=None        # Disable logging
        )

        # If transcriptions are provided, add subtitles
        if transcriptions:
            print("Adding subtitles to the final video...")

            # Close clips to free memory
            clip_with_audio.close()
            clip_without_audio.close()
            combined_clip.close()

            # Add subtitles to the combined video
            final_result = overlay_subtitles_on_video(cropped_output, transcriptions, final_output_path, subtitle_style)

            if final_result:
                print(f"✅ Final video with subtitles saved: {final_output_path}")
                print(f"✅ Cropped video (no subtitles) saved: {cropped_output}")
                return final_result
            else:
                print("❌ Error adding subtitles")
                return None
        else:
            # Close clips
            clip_with_audio.close()
            clip_without_audio.close()
            combined_clip.close()

            print(f"✅ Combined video saved: {cropped_output}")
            return cropped_output

    except Exception as e:
        print(f"❌ Error combining video and audio: {str(e)}")
        return None



if __name__ == "__main__":
    input_video_path = r'Out.mp4'
    output_video_path = 'Croped_output_video.mp4'
    final_video_path = 'final_video_with_audio.mp4'

    # Example transcriptions for testing
    test_transcriptions = [
        ("Hello everyone, welcome to this video", 0.0, 3.0),
        ("Today we're going to talk about AI", 3.0, 6.0),
        ("This is really exciting stuff", 6.0, 9.0)
    ]

    # Example: Use high smoothness to reduce flickering
    # You can use: 'low', 'medium', 'high', or 'ultra'
    smoothing, max_movement = set_face_tracking_smoothness('high')

    detect_faces_and_speakers(input_video_path, "DecOut.mp4")
    crop_to_vertical(input_video_path, output_video_path, smoothing, max_movement)
    combine_videos(input_video_path, output_video_path, final_video_path, test_transcriptions)



