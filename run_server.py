#!/usr/bin/env python3
"""
AI YouTube Shorts Generator - Web Interface
Run this script to start the web server
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import cv2
        import numpy
        import moviepy
        print("✅ Core dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = ['uploads', 'static', 'output_videos', 'templates']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directory ready: {directory}/")

def check_ffmpeg():
    """Check if FFmpeg is available"""
    ffmpeg_local = Path("ffmpeg-master-latest-win64-gpl/bin/ffmpeg.exe")
    
    if ffmpeg_local.exists():
        print("✅ Local FFmpeg found")
        return True
    
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ System FFmpeg found")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  FFmpeg not found - video processing may fail")
        print("Please install FFmpeg or place it in ffmpeg-master-latest-win64-gpl/bin/")
        return False

def main():
    """Main startup function"""
    print("🚀 AI YouTube Shorts Generator - Starting Web Server")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Check FFmpeg
    check_ffmpeg()
    
    print("\n" + "=" * 60)
    print("🎥 Starting AI YouTube Shorts Generator...")
    print("📱 Web Interface: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60 + "\n")
    
    # Import and run the Flask app
    try:
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()