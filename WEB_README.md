# AI YouTube Shorts Generator - Web Interface 🎥

A beautiful web interface for automatically generating YouTube Shorts from longer videos using AI.

## Features ✨

- **Multiple Input Methods:**
  - 📺 YouTube URL processing
  - 📁 Direct file upload (MP4, AVI, MOV, WEBM)
  - 💾 Google Drive link support

- **Professional Subtitle Styles:**
  - Modern: Clean white text with black outline
  - TikTok: Bold cyan text, uppercase
  - Instagram: Minimal with subtle background

- **Real-time Processing:**
  - Live progress updates
  - Streaming status information
  - Background processing

- **Optimized Performance:**
  - Multi-threaded video processing
  - Improved face tracking with reduced flickering
  - Faster encoding with H.264

## Quick Start 🚀

1. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the Web Server:**
   ```bash
   python run_server.py
   ```

3. **Open Your Browser:**
   - Navigate to: `http://localhost:5000`
   - Upload your video or paste a URL
   - Choose your subtitle style
   - Click "Generate AI Short"

## Usage Guide 📖

### YouTube Videos
1. Copy any YouTube video URL
2. Paste it in the YouTube URL tab
3. Select subtitle style and process

### File Upload
1. Click the "File Upload" tab
2. Drag & drop or click to select video file
3. Supports: MP4, AVI, MOV, WEBM (max 500MB)

### Google Drive
1. Share your video file on Google Drive
2. Set permissions to "Anyone with the link can view"
3. Copy the share link and paste it
4. The system will automatically download and process

## Subtitle Styles 🎨

### Modern Style
- Clean white text with black outline
- Professional appearance
- Great for educational content

### TikTok Style  
- Bold cyan text
- Uppercase formatting
- Eye-catching for viral content

### Instagram Style
- Minimal design with subtle background
- Off-white text with dark gray outline
- Perfect for story-style content

## Performance Improvements 🔧

### Face Tracking Enhancements
- **Position Change Threshold:** Only moves camera when face movement is significant (15+ pixels)
- **Reduced Smoothing:** Less jittery movement with improved smoothing algorithm
- **Optimized Detection:** Better face validation and consistency checking

### Subtitle Improvements
- **Fixed Line Spacing:** Proper spacing between multiple subtitle lines
- **Enhanced Styling:** Semi-transparent backgrounds and better contrast
- **Optimized Rendering:** Faster text overlay processing

### Video Processing
- **H.264 Encoding:** Faster compression and better quality
- **Multi-threading:** Parallel processing for improved speed
- **Reduced Bitrate:** Smaller files without quality loss (2500k vs 3000k)
- **Optimized Progress:** Less frequent debug output for better performance

## API Endpoints 🔌

### POST `/api/process`
Process a video with the following parameters:
- `input_type`: "youtube", "upload", or "gdrive"
- `url`: Video URL (for youtube/gdrive)
- `video_file`: File upload (for upload)
- `subtitle_style`: "modern", "tiktok", or "instagram"

Returns a streaming response with progress updates.

### GET `/api/status/<task_id>`
Get the current status of a processing task.

## File Structure 📁

```
AI-Youtube-Shorts-Generator/
├── app.py                 # Main Flask application
├── run_server.py         # Server startup script
├── templates/
│   └── index.html        # Web interface
├── Components/
│   ├── FileHandler.py    # File handling utilities
│   ├── FaceCrop.py       # Enhanced face tracking
│   ├── SubtitleGenerator.py # Improved subtitles
│   └── ...              # Other components
├── static/               # Generated videos
├── uploads/              # Uploaded files
└── output_videos/        # Organized output
```

## Configuration ⚙️

### Face Tracking Settings (in FaceCrop.py)
```python
FACE_TRACKING_CONFIG = {
    'default_smoothing_factor': 0.08,      # Smoothing level
    'default_max_movement_per_frame': 4,   # Max pixels per frame
    'position_change_threshold': 15        # Min change to trigger movement
}
```

### Server Settings (in app.py)
```python
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB limit
```

## Troubleshooting 🔧

### Common Issues

**Video not processing:**
- Check file format (MP4, AVI, MOV, WEBM supported)
- Ensure file size is under 500MB
- Verify internet connection for URL downloads

**Google Drive download fails:**
- Make sure file is shared with "Anyone with the link can view"
- Check if the URL is a valid Google Drive share link
- Large files may take longer to process

**Face tracking issues:**
- Ensure person's face is clearly visible
- Video should have good lighting
- Avoid videos with multiple people in frame

**Subtitle problems:**
- Check if audio contains clear speech
- Ensure video has audio track
- Try different subtitle styles

### Performance Tips

1. **Use shorter videos** (under 10 minutes) for faster processing
2. **Close other applications** to free up system resources
3. **Use MP4 format** for best compatibility
4. **Ensure stable internet** for URL-based processing

## Technical Details 🔬

### Processing Pipeline
1. **Download/Upload:** Acquire video file
2. **Validation:** Check video format and properties
3. **Audio Extraction:** Extract audio for transcription
4. **Transcription:** Convert speech to text using Whisper
5. **Highlight Detection:** Find most engaging segment using AI
6. **Face Tracking:** Track and center face in frame
7. **Vertical Crop:** Convert to 9:16 aspect ratio
8. **Subtitle Overlay:** Add styled text to video
9. **Final Export:** Combine all elements into final video

### Dependencies
- **Flask:** Web framework
- **OpenCV:** Video processing and face detection
- **MoviePy:** Video editing and effects
- **Whisper:** Speech-to-text transcription
- **PyTube:** YouTube video downloading

## Contributing 🤝

To contribute to this project:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License 📄

This project is open source. Please check the main repository for license details.

---

**Made with ❤️ for content creators**

For more information, visit the main project repository.