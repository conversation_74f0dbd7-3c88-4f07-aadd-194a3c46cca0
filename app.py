from flask import Flask, render_template, request, jsonify, send_file, Response
import os
import json
import tempfile
import shutil
from werkzeug.utils import secure_filename
import threading
import time
from datetime import datetime
import re
import requests
from urllib.parse import urlparse, parse_qs

# Import your existing components
from Components.YoutubeDownloader import download_youtube_video
from Components.Edit import extractAudio, crop_video
from Components.Transcription import transcribeAudio
from Components.LanguageTasks import GetHighlight
from Components.FaceCrop import crop_to_vertical, combine_videos
from Components.SubtitleGenerator import create_output_folder, generate_output_filename
from Components.FileHandler import extract_gdrive_id, download_from_gdrive, validate_video_file, sanitize_filename, cleanup_temp_files

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static', exist_ok=True)

# Global variable to store processing progress
processing_status = {}


def update_progress(task_id, progress, message):
    """Update processing progress"""
    processing_status[task_id] = {
        'progress': progress,
        'message': message,
        'timestamp': datetime.now().isoformat()
    }

def cleanup_intermediate_files():
    """Clean up intermediate files created during processing"""
    intermediate_files = [
        "Out.mp4",
        "croped.mp4", 
        "DecOut.mp4",
        "audio.wav",
        "Final.mp4",
        "temp-audio.m4a"
    ]
    
    for file in intermediate_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"Cleaned up intermediate file: {file}")
            except Exception as e:
                print(f"Warning: Could not remove {file}: {e}")

def process_video_task(task_id, input_path, subtitle_style):
    """Process video in background thread"""
    try:
        update_progress(task_id, 10, "Extracting audio...")
        
        # Extract audio
        audio_path = extractAudio(input_path)
        if not audio_path:
            raise Exception("Failed to extract audio from video")
        
        update_progress(task_id, 25, "Transcribing audio...")
        
        # Transcribe audio
        transcriptions = transcribeAudio(audio_path)
        if len(transcriptions) == 0:
            raise Exception("No transcriptions found in audio")
        
        update_progress(task_id, 40, "Finding highlight segment...")
        
        # Get highlight segment
        trans_text = ""
        for text, start, end in transcriptions:
            trans_text += f"{start} - {end}: {text}"
        
        start_time, end_time = GetHighlight(trans_text)
        if start_time == 0 and end_time == 0:
            raise Exception("Could not find highlight segment")
        
        update_progress(task_id, 55, "Cropping video to highlight...")
        
        # Crop video to highlight
        cropped_output = "Out.mp4"
        crop_video(input_path, cropped_output, start_time, end_time)
        
        update_progress(task_id, 70, "Converting to vertical format...")
        
        # Convert to vertical
        vertical_output = "croped.mp4"
        crop_to_vertical(cropped_output, vertical_output)
        
        update_progress(task_id, 85, "Adding subtitles...")
        
        # Filter transcriptions for cropped segment
        cropped_transcriptions = []
        for text, trans_start, trans_end in transcriptions:
            if trans_start >= start_time and trans_end <= end_time:
                adjusted_start = trans_start - start_time
                adjusted_end = trans_end - start_time
                cropped_transcriptions.append((text, adjusted_start, adjusted_end))
            elif trans_start < end_time and trans_end > start_time:
                adjusted_start = max(0, trans_start - start_time)
                adjusted_end = min(end_time - start_time, trans_end - start_time)
                if adjusted_end > adjusted_start:
                    cropped_transcriptions.append((text, adjusted_start, adjusted_end))
        
        # Combine video with audio and add subtitles
        final_output_path = combine_videos(cropped_output, vertical_output, "Final.mp4", 
                                         cropped_transcriptions, subtitle_style)
        
        if not final_output_path:
            raise Exception("Failed to generate final video")
        
        update_progress(task_id, 95, "Finalizing...")
        
        # Create static URL for frontend access
        output_filename = os.path.basename(final_output_path)
        static_path = os.path.join('static', output_filename)
        
        # Copy to static folder for web access
        shutil.copy2(final_output_path, static_path)
        
        # Get video info
        import cv2
        cap = cv2.VideoCapture(final_output_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        # Clean up intermediate files
        cleanup_intermediate_files()
        
        update_progress(task_id, 100, "Complete!")
        
        # Store final result
        processing_status[task_id]['result'] = {
            'video_url': f'/static/{output_filename}',
            'duration': f"{duration:.1f}s",
            'resolution': f"{width}x{height}",
            'style': subtitle_style,
            'filename': output_filename
        }
        
    except Exception as e:
        processing_status[task_id]['error'] = str(e)
        cleanup_intermediate_files()

@app.route('/')
def index():
    """Serve the main page"""
    return render_template('index.html')

@app.route('/api/process', methods=['POST'])
def process_video():
    """Process video API endpoint"""
    try:
        input_type = request.form.get('input_type')
        subtitle_style = request.form.get('subtitle_style', 'modern')
        
        # Generate unique task ID
        task_id = f"task_{int(time.time())}_{os.getpid()}"
        
        # Initialize progress
        update_progress(task_id, 0, "Starting...")
        
        video_path = None
        
        if input_type == 'youtube':
            url = request.form.get('url')
            if not url:
                return jsonify({'error': 'YouTube URL is required'}), 400
            
            update_progress(task_id, 5, "Downloading from YouTube...")
            video_path = download_youtube_video(url, auto_select=True)
            if not video_path:
                return jsonify({'error': 'Failed to download YouTube video'}), 400
            
            # Convert webm to mp4 if needed
            if video_path.endswith('.webm'):
                video_path = video_path.replace('.webm', '.mp4')
                
        elif input_type == 'upload':
            file = request.files.get('video_file')
            if not file or file.filename == '':
                return jsonify({'error': 'No video file uploaded'}), 400
            
            # Save uploaded file
            filename = secure_filename(file.filename)
            video_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(video_path)
            
        elif input_type == 'gdrive':
            url = request.form.get('url')
            if not url:
                return jsonify({'error': 'Google Drive URL is required'}), 400
            
            file_id = extract_gdrive_id(url)
            if not file_id:
                return jsonify({'error': 'Invalid Google Drive URL. Please use a valid share link.'}), 400
            
            video_path = os.path.join(app.config['UPLOAD_FOLDER'], f"gdrive_{file_id}.mp4")
            
            # Use improved download function with progress callback
            def progress_callback(progress, message):
                update_progress(task_id, progress, message)
            
            if not download_from_gdrive(file_id, video_path, progress_callback):
                return jsonify({'error': 'Failed to download from Google Drive. Please check the link and permissions.'}), 400
        
        else:
            return jsonify({'error': 'Invalid input type'}), 400
        
        if not os.path.exists(video_path):
            return jsonify({'error': 'Video file not found'}), 400
        
        # Validate video file
        validation_result = validate_video_file(video_path)
        if not validation_result['valid']:
            # Clean up invalid file
            try:
                os.remove(video_path)
            except:
                pass
            return jsonify({'error': f'Invalid video file: {validation_result["error"]}'}), 400
        
        # Start processing in background thread
        thread = threading.Thread(target=process_video_task, 
                                 args=(task_id, video_path, subtitle_style))
        thread.daemon = True
        thread.start()
        
        # Return streaming response for progress updates
        def generate_progress():
            while task_id in processing_status:
                status = processing_status[task_id]
                yield f"data: {json.dumps(status)}\n\n"
                
                if status.get('progress', 0) >= 100 or 'error' in status:
                    break
                    
                time.sleep(1)
        
        return Response(generate_progress(), mimetype='text/event-stream')
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<task_id>')
def get_status(task_id):
    """Get processing status"""
    if task_id in processing_status:
        return jsonify(processing_status[task_id])
    return jsonify({'error': 'Task not found'}), 404

@app.route('/static/<filename>')
def serve_static(filename):
    """Serve static files"""
    return send_file(os.path.join('static', filename))

@app.route('/favicon.ico')
def favicon():
    """Serve favicon"""
    return send_file(os.path.join('static', 'favicon.ico'))

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large. Maximum size is 500MB.'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starting AI YouTube Shorts Generator...")
    print("📁 Frontend available at: http://localhost:5000")
    print("🎥 Upload your videos and start creating shorts!")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)