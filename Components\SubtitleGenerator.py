import os
import re
import cv2
import numpy as np
from moviepy.editor import VideoFileClip
import textwrap
from datetime import datetime

# Add FFmpeg to PATH
ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                          "ffmpeg-master-latest-win64-gpl", "bin")
if os.path.exists(ffmpeg_path):
    os.environ["PATH"] += os.pathsep + ffmpeg_path
    print(f"Added FFmpeg to PATH in SubtitleGenerator.py: {ffmpeg_path}")

def create_output_folder():
    """Create output_videos folder if it doesn't exist"""
    output_folder = "output_videos"
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"Created output folder: {output_folder}")
    return output_folder

def generate_output_filename(original_name, suffix="_with_subtitles"):
    """Generate output filename with timestamp and suffix"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    name, ext = os.path.splitext(original_name)
    return f"{name}{suffix}_{timestamp}{ext}"

def format_time_for_srt(seconds):
    """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

def generate_srt_file(transcriptions, srt_path="subtitles.srt"):
    """Generate SRT subtitle file from transcription data"""
    try:
        with open(srt_path, 'w', encoding='utf-8') as f:
            for i, (text, start, end) in enumerate(transcriptions, 1):
                # Clean up the text
                text = text.strip()
                if not text:
                    continue
                
                # Format timestamps
                start_time = format_time_for_srt(start)
                end_time = format_time_for_srt(end)
                
                # Write SRT entry
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")
        
        print(f"SRT file generated: {srt_path}")
        return srt_path
    except Exception as e:
        print(f"Error generating SRT file: {e}")
        return None

def add_text_to_frame(frame, text, style='modern'):
    """Add styled text to a video frame using OpenCV - optimized for 9:16 vertical videos"""
    try:
        height, width = frame.shape[:2]

        # Clean and wrap text for better readability (optimized for minimal style)
        clean_text = text.strip().upper() if style == 'tiktok' else text.strip()
        # Shorter lines for cleaner look
        wrap_width = {'modern': 22, 'tiktok': 18, 'instagram': 24}.get(style, 22)
        wrapped_lines = textwrap.fill(clean_text, width=wrap_width).split('\n')
        
        # Limit to max 2 lines for minimal design
        if len(wrapped_lines) > 2:
            wrapped_lines = [' '.join(wrapped_lines[:2])]

        # Calculate appropriate font scale based on video width (for 9:16 videos)
        base_font_scale = max(0.3, width / 400.0)  # Minimum 0.3, scales with width

        # Professional minimal style configurations optimized for vertical videos
        styles = {
            'modern': {
                'font': cv2.FONT_HERSHEY_DUPLEX,  # Cleaner font
                'font_scale': base_font_scale * 0.65,  # Smaller, cleaner text
                'color': (255, 255, 255),  # Pure white
                'stroke_color': (0, 0, 0),  # Deep black outline
                'thickness': 1,
                'stroke_thickness': 2,  # Thinner outline
                'position_y': 0.88,  # Lower position
                'letter_spacing': 2,  # Add letter spacing
                'background': True,  # Add subtle background
                'bg_color': (0, 0, 0, 80)  # Semi-transparent black
            },
            'tiktok': {
                'font': cv2.FONT_HERSHEY_DUPLEX,
                'font_scale': base_font_scale * 0.7,
                'color': (0, 220, 255),  # Bright cyan
                'stroke_color': (0, 0, 0),
                'thickness': 1,
                'stroke_thickness': 2,
                'position_y': 0.9,
                'letter_spacing': 1,
                'background': False
            },
            'instagram': {
                'font': cv2.FONT_HERSHEY_DUPLEX,
                'font_scale': base_font_scale * 0.6,
                'color': (245, 245, 245),  # Off-white
                'stroke_color': (40, 40, 40),  # Dark gray
                'thickness': 1,
                'stroke_thickness': 1,
                'position_y': 0.89,
                'letter_spacing': 1,
                'background': True,
                'bg_color': (20, 20, 20, 60)
            }
        }

        current_style = styles.get(style, styles['modern'])

        # Calculate line height based on font scale (proper spacing between lines)
        line_height = int(40 * current_style['font_scale'])  # Increased spacing
        total_height = len(wrapped_lines) * line_height

        # Position subtitles at the bottom of the frame
        start_y = int(height * current_style['position_y'] - total_height)

        # Draw each line of text with enhanced styling
        for i, line in enumerate(wrapped_lines):
            if not line.strip():
                continue

            # Get text size for centering
            (text_width, text_height), _ = cv2.getTextSize(
                line, current_style['font'], current_style['font_scale'], current_style['thickness']
            )

            # Center the text horizontally
            x = (width - text_width) // 2
            y = start_y + (i * line_height) + text_height

            # Ensure text doesn't go outside frame boundaries
            margin = 15
            if y > height - margin:
                y = height - margin
            if x < margin:
                x = margin
            if x + text_width > width - margin:
                x = width - text_width - margin

            # Draw background if enabled
            if current_style.get('background', False):
                bg_color = current_style.get('bg_color', (0, 0, 0, 80))
                padding = 8
                cv2.rectangle(frame, 
                            (x - padding, y - text_height - padding//2), 
                            (x + text_width + padding, y + padding//2),
                            bg_color[:3], -1)

            # Draw stroke (outline) - slightly blurred for smoother look
            if current_style['stroke_thickness'] > 0:
                cv2.putText(frame, line, (x, y), current_style['font'],
                           current_style['font_scale'], current_style['stroke_color'],
                           current_style['stroke_thickness'], cv2.LINE_AA)

            # Draw main text with anti-aliasing
            cv2.putText(frame, line, (x, y), current_style['font'],
                       current_style['font_scale'], current_style['color'],
                       current_style['thickness'], cv2.LINE_AA)

        return frame
    except Exception as e:
        print(f"Error adding text to frame: {e}")
        return frame

def process_video_with_subtitles(input_path, output_path, transcriptions, style='modern'):
    """Process video and add subtitles using OpenCV"""
    try:
        print(f"Processing video with subtitles using '{style}' style...")

        # Open input video
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            print("Error: Could not open input video")
            return None

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Create video writer with optimized codec
        fourcc = cv2.VideoWriter_fourcc(*'H264')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        frame_count = 0

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Calculate current time in seconds
            current_time = frame_count / fps

            # Find active subtitle for current time
            current_subtitle = None
            for text, start, end in transcriptions:
                if start <= current_time <= end:
                    current_subtitle = text
                    break

            # Add subtitle to frame if found
            if current_subtitle:
                frame = add_text_to_frame(frame, current_subtitle, style)

            # Write frame
            out.write(frame)
            frame_count += 1

            # Progress indicator (less frequent for performance)
            if frame_count % 200 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"Subtitle processing: {progress:.1f}%")

        # Clean up
        cap.release()
        out.release()

        print(f"Video with subtitles saved: {output_path}")
        return output_path

    except Exception as e:
        print(f"Error processing video with subtitles: {e}")
        return None

def overlay_subtitles_on_video(video_path, transcriptions, output_path, style='modern'):
    """Overlay subtitles on video and save the result"""
    try:
        # First, create video with subtitles using OpenCV
        temp_video_path = "temp_video_with_subtitles.mp4"
        result = process_video_with_subtitles(video_path, temp_video_path, transcriptions, style)

        if not result:
            print("Failed to process video with subtitles")
            return None

        # Now use MoviePy to combine with audio and convert to final format
        print("Adding audio to subtitled video...")
        original_video = VideoFileClip(video_path)
        subtitled_video = VideoFileClip(temp_video_path)

        # Set audio from original video
        final_video = subtitled_video.set_audio(original_video.audio)

        # Write final video with optimized settings
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='faster',  # Faster encoding
            threads=4,        # Use multiple threads
            bitrate='2500k',  # Optimized bitrate
            verbose=False,    # Reduce output
            logger=None       # Disable logging for speed
        )

        # Clean up
        original_video.close()
        subtitled_video.close()
        final_video.close()

        # Remove temporary file
        if os.path.exists(temp_video_path):
            os.remove(temp_video_path)

        print(f"Video with subtitles saved: {output_path}")
        return output_path

    except Exception as e:
        print(f"Error overlaying subtitles: {e}")
        return None



if __name__ == "__main__":
    # Test the subtitle generation
    test_transcriptions = [
        ("Hello everyone, welcome to this video", 0.0, 3.0),
        ("Today we're going to talk about AI", 3.0, 6.0),
        ("This is really exciting stuff", 6.0, 9.0)
    ]
    
    # Generate SRT file
    srt_file = generate_srt_file(test_transcriptions)
    print(f"Test SRT file created: {srt_file}")
